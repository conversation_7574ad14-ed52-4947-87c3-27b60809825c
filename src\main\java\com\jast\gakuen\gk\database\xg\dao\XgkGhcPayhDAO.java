/*
 * XgkGhcPayhDAO.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved. 
 * 会社名  ：日本システム技術株式会社 
 *
 */
package com.jast.gakuen.gk.database.xg.dao;

import com.jast.gakuen.common.database.gh.base.GhcPayhValue;
import com.jast.gakuen.common.database.gh.dao.GhcPayhDAO;
import com.jast.gakuen.common.database.gh.entity.GhcPayhAR;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.database.RowValue;
import com.jast.gakuen.core.common.database.RxPreparedStatement;
import com.jast.gakuen.core.common.exception.DbException;
import com.jast.gakuen.core.common.exception.OverTheLimitException;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 納付金配当テーブル(ghc_payh) DAO拡張クラス<br>
 * 
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class XgkGhcPayhDAO extends GhcPayhDAO {

	/** SQL文定義 */
	private enum Statement {

		/** SQL_FIND_BY_NENDO_GYOMU_CD_PAY_CD_SHUBETSU */
		SQL_FIND_BY_NENDO_GYOMU_CD_PAY_CD_SHUBETSU(
				"select gh.* from ghc_payh gh "
						+ " inner join xgm_pay xg on xg.pay_cd = gh.pay_cd "
						+ " where gh.nendo = ? "
						+ "   and xg.gyomu_cd = ? "
						+ "   and substr(gh.pay_cd, 1, 1) in (@noufuKinShubetsus@) "
						+ " order by gh.nendo, gh.pattern_cd, gh.bunno_kbn_cd ");

		/** SQL文 */
		private final String sql;

		/**
		 * コンストラクタ
		 * 
		 * @param sql SQL
		 */
		Statement(final String sql) {
			this.sql = sql;
		}
	}

	/** プリペアドステートメントMAP */
	private final Map<Statement, RxPreparedStatement> pstmtMap = new HashMap<>();

	/**
	 * GhcPayhARインスタンス生成
	 *
	 * @param dbs DbSession
	 * @param rv RowValue
	 * @return GhcPayhARインスタンス
	 */
	@Override
	protected GhcPayhAR createActiveRecord(final DbSession dbs, final RowValue rv) {
		return new GhcPayhAR(dbs, (GhcPayhValue) rv);
	}
	
	/**
	 * 年度、業務コード、納付金種別で納付金配当データを取得する。<br>
	 *
	 * @param nendo 年度
	 * @param gyomuCd 業務コード
	 * @param noufuKinShubetsuList 納付金種別リスト
	 * @return 検索結果
	 * @throws DbException DB例外
	 * @throws OverTheLimitException 処理上限例外
	 */
	public List<GhcPayhAR> findByNendoGyomuCdPayCdShubetsu(
			final int nendo,
			final String gyomuCd,
			final List<String> noufuKinShubetsuList
			) throws DbException, OverTheLimitException {

		List<GhcPayhAR> result = null;

		try {
			// 納付金種別リストが空またはnullの場合は空のリストを返す
			if (noufuKinShubetsuList == null || noufuKinShubetsuList.isEmpty()) {
				return new java.util.ArrayList<>();
			}

			// 納付金種別リストをSQL用にクォート処理
			List<String> quotedList = noufuKinShubetsuList.stream()
					.map(s -> "'" + s + "'")
					.collect(java.util.stream.Collectors.toList());

			Statement stmt = Statement.SQL_FIND_BY_NENDO_GYOMU_CD_PAY_CD_SHUBETSU;
			RxPreparedStatement pstmt = prepareQueryStatement(
					stmt.sql.replace("@noufuKinShubetsus@", String.join(", ", quotedList)));

			int i = 0;
			pstmt.setParam(++i, nendo);
			pstmt.setParam(++i, gyomuCd);

			try (ResultSet rs = pstmt.executeQuery()) {
				result = getActiveRecordList(rs);
			}
		} catch (OverTheLimitException oe) {
			throw oe;
		} catch (Exception e) {
			throw new DbException(e);
		}

		return result;
	}
}
