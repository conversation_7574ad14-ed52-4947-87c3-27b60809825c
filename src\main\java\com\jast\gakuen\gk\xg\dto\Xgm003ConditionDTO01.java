/*
 * Xgm003ConditionDTO01.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import java.util.Date;

import com.jast.gakuen.core.common.BaseDTO;
import com.jast.gakuen.core.common.annotation.RxOpt;
import com.jast.gakuen.core.common.annotation.RxValidationForUI;

import lombok.Getter;
import lombok.Setter;

/**
 * 請求書出力DTO
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003ConditionDTO01 extends BaseDTO {

	/**
	 * 発行日付
	 */
	@RxValidationForUI(id = "common.hakkoDate.0.label", hissuFlg = true)
	@RxOpt(value = "hattyuDate")
	private Date hattyuDate;

	/**
	 * 通信欄区分
	 */
	@RxOpt(value = "tsuukyakKbn")
	private boolean tsuukyakKbn;

	/**
	 * 出力区分
	 */
	@RxOpt(value = "outputKbn")
	private String outputKbn;

	/**
	 * 通信欄
	 */
	@RxOpt(value = "tsuukyak")
	private String tsuukyak;

	/**
	 * 期限区分
	 */
	@RxOpt(value = "limitKbn")
	private boolean limitKbn;

	/**
	 * 納入期限
	 */
	@RxOpt(value = "payLimit")
	private Date payLimit;

	/**
	 * 有効期限
	 */
	@RxOpt(value = "yukouLimit")
	private Date yukouLimit;
}
